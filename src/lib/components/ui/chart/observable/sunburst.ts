import type { BudgetLineItemBase, WbsItemWithBudgetData } from '$lib/budget_utils';
import * as d3 from 'd3';

// Copyright 2021 Observable, Inc.
// Released under the ISC license.
// https://observablehq.com/@d3/sunburst

// Type for data that can be either raw data or pre-processed hierarchy
type SunburstData<T> = d3.HierarchyNode<T> | T[] | T;

// Extended hierarchy node type with partition layout properties
interface PartitionNode<T> extends d3.HierarchyNode<T> {
	x0: number;
	x1: number;
	y0: number;
	y1: number;
	index?: number;
}

export function Sunburst<T = WbsItemWithBudgetData<BudgetLineItemBase>>(
	data: SunburstData<T>,
	{
		// data is either tabular (array of objects), hierarchy (nested objects), or pre-processed d3.HierarchyNode
		path, // as an alternative to id and parentId, returns an array identifier, imputing internal nodes
		id = Array.isArray(data) ? (d: T) => (d as T & { id: string }).id : null, // if tabular data, given a d in data, returns a unique identifier (string)
		parentId = Array.isArray(data)
			? (d: T) => (d as T & { parentId?: string }).parentId ?? null
			: null, // if tabular data, given a node d, returns its parent's identifier
		children, // if hierarchical data, given a d in data, returns its children
		value, // given a node d, returns a quantitative value (for area encoding; null for count)
		sort = (a: d3.HierarchyNode<T>, b: d3.HierarchyNode<T>) => d3.descending(a.value, b.value), // how to sort nodes prior to layout
		label, // given a node d, returns the name to display on the rectangle
		title, // given a node d, returns its hover text
		link, // given a node d, its link (if any)
		linkTarget = '_blank', // the target attribute for links (if any)
		width = 640, // outer width, in pixels
		height = 400, // outer height, in pixels
		margin = 1, // shorthand for margins
		marginTop = margin, // top margin, in pixels
		marginRight = margin, // right margin, in pixels
		marginBottom = margin, // bottom margin, in pixels
		marginLeft = margin, // left margin, in pixels
		padding = 1, // separation between arcs
		radius = Math.min(width - marginLeft - marginRight, height - marginTop - marginBottom) / 2, // outer radius
		color = d3.interpolateRainbow, // color scheme, if any
	}: {
		path?: (d: T) => string;
		id?: ((d: T) => string) | null;
		parentId?: ((d: T) => string | null) | null;
		children?: (d: T) => T[] | undefined;
		value?: ((d: T) => number) | null;
		sort?: ((a: d3.HierarchyNode<T>, b: d3.HierarchyNode<T>) => number) | null;
		label?: ((d: T, n: PartitionNode<T>) => string) | null;
		title?: ((d: T, n: PartitionNode<T>) => string) | null;
		link?: ((d: T, n: PartitionNode<T>) => string) | null;
		linkTarget?: string;
		width?: number;
		height?: number;
		margin?: number;
		marginTop?: number;
		marginRight?: number;
		marginBottom?: number;
		marginLeft?: number;
		padding?: number;
		radius?: number;
		color?: (t: number) => string;
	} = {},
) {
	console.log({ data });

	// Check if data is already a d3.HierarchyNode
	const isHierarchyNode = (obj: unknown): obj is d3.HierarchyNode<T> => {
		return (
			obj &&
			typeof obj === 'object' &&
			'data' in obj &&
			'children' in obj &&
			'value' in obj &&
			'depth' in obj
		);
	};

	let root: d3.HierarchyNode<T>;

	if (isHierarchyNode(data)) {
		// Data is already a processed hierarchy node, use it directly
		root = data;
		console.log('Using pre-processed hierarchy node:', { root });
	} else {
		// Process raw data using existing logic
		if (path != null) {
			root = d3.stratify<T>().path(path)(data as T[]);
		} else if (id != null || parentId != null) {
			const stratifyFn = d3.stratify<T>();
			if (id != null) stratifyFn.id(id);
			if (parentId != null) stratifyFn.parentId(parentId);
			root = stratifyFn(data as T[]);
		} else {
			root = d3.hierarchy(data as T, children);
		}

		console.log('Created hierarchy from raw data:', { root });

		// Compute the values of internal nodes by aggregating from the leaves.
		if (value == null) {
			root.count();
		} else {
			root.sum((d) => Math.max(0, value(d)));
		}
	}

	// Sort the leaves (typically by descending value for a pleasing layout).
	if (sort != null) root.sort(sort);

	// Compute the partition layout. Note polar coordinates: x is angle and y is radius.
	d3.partition<T>().size([2 * Math.PI, radius])(root);

	// Cast root to PartitionNode after partition layout is applied
	const partitionRoot = root as PartitionNode<T>;

	// Construct a color scale.
	if (color != null && partitionRoot.children) {
		const _colorScale = d3.scaleSequential([0, partitionRoot.children.length - 1], color);
		partitionRoot.children.forEach((child, i) => {
			(child as PartitionNode<T>).index = i;
		});
	}

	// Construct an arc generator.
	const arc = d3
		.arc<PartitionNode<T>>()
		.startAngle((d) => d.x0)
		.endAngle((d) => d.x1)
		.padAngle((d) => Math.min((d.x1 - d.x0) / 2, (2 * padding) / radius))
		.padRadius(radius / 2)
		.innerRadius((d) => d.y0)
		.outerRadius((d) => d.y1 - padding);

	const svg = d3
		.create('svg')
		.attr('viewBox', [
			marginRight - marginLeft - width / 2,
			marginBottom - marginTop - height / 2,
			width,
			height,
		])
		.attr('width', width)
		.attr('height', height)
		.attr('style', 'max-width: 100%; height: intrinsic;')
		.attr('font-family', 'sans-serif')
		.attr('font-size', 10)
		.attr('text-anchor', 'middle');

	const descendants = partitionRoot.descendants() as PartitionNode<T>[];
	const cell = svg
		.selectAll('a')
		.data(descendants)
		.join('a')
		.attr('xlink:href', link == null ? null : (d) => link(d.data, d))
		.attr('target', link == null ? null : linkTarget);

	cell
		.append('path')
		.attr('d', arc)
		.attr('fill', (d) => {
			// Root node gets neutral color
			if (d.depth === 0) return '#f0f0f0';

			// Use a simple hash-based color assignment
			const dataWithCode = d.data as T & { code?: string };
			const hash = dataWithCode.code ? dataWithCode.code.split('.')[0] : d.depth.toString();
			const colorIndex = parseInt(hash.replace(/\D/g, '') || '0') % 10;
			const colors = [
				'#1f77b4',
				'#ff7f0e',
				'#2ca02c',
				'#d62728',
				'#9467bd',
				'#8c564b',
				'#e377c2',
				'#7f7f7f',
				'#bcbd22',
				'#17becf',
			];

			// Adjust opacity based on depth
			const baseColor = colors[colorIndex];
			const opacity = Math.max(0.4, 1 - (d.depth - 1) * 0.15);
			const color = d3.color(baseColor);
			if (color) {
				color.opacity = opacity;
				return color.toString();
			}

			return baseColor;
		})
		.attr('fill-opacity', 1);

	if (label != null)
		cell
			.filter((d) => ((d.y0 + d.y1) / 2) * (d.x1 - d.x0) > 10)
			.append('text')
			.attr('transform', (d) => {
				if (!d.depth) return '';
				const x = (((d.x0 + d.x1) / 2) * 180) / Math.PI;
				const y = (d.y0 + d.y1) / 2;
				return `rotate(${x - 90}) translate(${y},0) rotate(${x < 180 ? 0 : 180})`;
			})
			.attr('dy', '0.32em')
			.text((d) => label(d.data, d));

	if (title != null) cell.append('title').text((d) => title(d.data, d));

	return svg.node();
}
