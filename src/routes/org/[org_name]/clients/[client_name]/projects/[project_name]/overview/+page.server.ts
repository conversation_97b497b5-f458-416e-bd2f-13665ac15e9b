import { requireProject } from '$lib/server/auth';
import { requireUser } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import type { PageServerLoad } from './$types';

export const load = (async ({ params, locals, cookies, depends }) => {
	depends('project:budget');

	await requireUser();

	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select(
			`*,
				client!inner(name, client_id, organization(name, org_id)),
				wbs_library:wbs_library(wbs_library_item(*)),
				budget_line_item_current(*)`,
		)
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${encodeURIComponent(params.org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Extract related data returned from the join
	const allWbsItems = projectData.wbs_library?.wbs_library_item || [];
	const rawCurrentItems =
		projectData.budget_line_item_current.map((bli) => {
			return {
				budget_snapshot_line_item_id: bli.budget_line_item_id, // Map to expected field name
				budget_snapshot_id: 'current', // Use 'current' as a pseudo snapshot ID
				wbs_library_item_id: bli.wbs_library_item_id,
				quantity: bli.quantity,
				unit_rate: bli.unit_rate,
				factor: bli.factor,
				label: 'current',
			};
		}) || [];

	// First, get all project stages for this project
	const { data: projectStages, error: stagesError } = await supabase
		.from('project_stage')
		.select('project_stage_id')
		.eq('project_id', projectData.project_id);

	if (stagesError) {
		console.error('Error fetching project stages:', stagesError);
	}

	// Then fetch budget snapshots for those project stages
	const { data: budgetSnapshots, error: snapshotsError } = await supabase
		.from('budget_snapshot')
		.select(
			`
			budget_snapshot_id,
			project_stage_id,
			freeze_date,
			freeze_reason,
			created_by_user_id,
			created_at,
			updated_at,
			project_stage (
				name,
				stage_order,
				stage,
				project_id
			)
		`,
		)
		.in('project_stage_id', projectStages?.map((stage) => stage.project_stage_id) || [])
		.order('freeze_date', { ascending: true });

	if (snapshotsError) {
		console.error('Error fetching budget snapshots:', snapshotsError);
		// Continue without snapshots rather than failing completely
	}

	// Fetch budget snapshot line items for all snapshots in a single query
	const snapshotsWithItems: Array<{
		snapshot: NonNullable<typeof budgetSnapshots>[number];
		budgetItems: Array<{
			budget_snapshot_line_item_id: string;
			budget_snapshot_id: string;
			wbs_library_item_id: string;
			quantity: number | null;
			unit_rate: number | null;
			factor: number | null;
			label: string;
		}>;
	}> = [];

	if (budgetSnapshots && budgetSnapshots.length > 0) {
		// Extract all snapshot IDs for the IN query
		const snapshotIds = budgetSnapshots.map((snapshot) => snapshot.budget_snapshot_id);

		// Fetch all snapshot items at once
		const { data: allSnapshotItems, error: itemsError } = await supabase
			.from('budget_snapshot_line_item')
			.select('*')
			.in('budget_snapshot_id', snapshotIds);

		if (itemsError) {
			console.error('Error fetching snapshot line items:', itemsError);
		} else {
			// Group items by snapshot ID for efficient lookup
			const itemsBySnapshotId = new Map<string, Array<Record<string, unknown>>>();
			allSnapshotItems?.forEach((item) => {
				if (!itemsBySnapshotId.has(item.budget_snapshot_id)) {
					itemsBySnapshotId.set(item.budget_snapshot_id, []);
				}
				itemsBySnapshotId.get(item.budget_snapshot_id)!.push(item);
			});

			// Build the snapshots with their items
			budgetSnapshots.forEach((snapshot) => {
				const snapshotItems = itemsBySnapshotId.get(snapshot.budget_snapshot_id) || [];
				snapshotsWithItems.push({
					snapshot: snapshot,
					budgetItems: snapshotItems.map((item) => ({
						budget_snapshot_line_item_id: item.budget_snapshot_line_item_id,
						budget_snapshot_id: item.budget_snapshot_id,
						wbs_library_item_id: item.wbs_library_item_id,
						quantity: item.quantity,
						unit_rate: item.unit_rate,
						factor: item.factor,
						label: `snapshot-${snapshot.project_stage?.stage_order || 'unknown'}`,
					})),
				});
			});
		}
	}

	// Check user permissions
	const { data: canEditProject } = await supabase.rpc('can_modify_project', {
		project_id_param: projectData.project_id,
	});

	const wbsItems =
		allWbsItems?.map((i) => ({
			label: `${i.code}: ${i.description}`,
			value: i.wbs_library_item_id,
		})) || [];

	return {
		client: projectData.client,
		project: projectData,
		wbsItems,
		allWbsItems: allWbsItems || [],
		rawCurrentItems: rawCurrentItems || [],
		budgetSnapshots: budgetSnapshots || [],
		snapshotsWithItems: snapshotsWithItems || [],
		canEditProject: canEditProject || false,
	};
}) satisfies PageServerLoad;
